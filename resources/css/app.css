@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: "Instrument Sans", ui-sans-serif, system-ui, sans-serif,
        "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
        "Noto Color Emoji";
}

@theme inline {
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
    --color-primary-100: var(--primary-100);
    --color-primary-200: var(--primary-200);
    --color-primary-300: var(--primary-300);
    --color-primary-400: var(--primary-400);
    --color-primary-500: var(--primary-500);
    --color-primary-600: var(--primary-600);
    --color-primary-700: var(--primary-700);
    --color-primary-800: var(--primary-800);
    --color-primary-900: var(--primary-900);
    --color-primary-950: var(--primary-950);
    --color-secondary-100: var(--secondary-100);
    --color-secondary-200: var(--secondary-200);
    --color-secondary-300: var(--secondary-300);
    --color-secondary-400: var(--secondary-400);
    --color-secondary-500: var(--secondary-500);
    --color-secondary-600: var(--secondary-600);
    --color-secondary-700: var(--secondary-700);
    --color-secondary-800: var(--secondary-800);
    --color-secondary-900: var(--secondary-900);
    --color-secondary-950: var(--secondary-950);
    --color-background-100: var(--background-100);
    --color-background-200: var(--background-200);
    --color-background-300: var(--background-300);
    --color-background-400: var(--background-400);
    --color-background-500: var(--background-500);
    --color-background-600: var(--background-600);
    --color-background-700: var(--background-700);
    --color-background-800: var(--background-800);
    --color-background-900: var(--background-900);
    --color-background-950: var(--background-950);
}

:root {
    --radius: 0.625rem;
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.205 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);

    --primary-100: oklch(0.9455 0.005 165);
    --primary-200: oklch(0.9175 0.006 170.44);
    --primary-300: oklch(0.8291 0.0139 167.1);
    --primary-400: oklch(0.4137 0.0501 162.06);
    --primary-500: oklch(0.3864 0.047 161.36);
    --primary-600: oklch(0.3561 0.0412 163.32);
    --primary-700: oklch(0.3422 0.0396 161.59);
    --primary-800: oklch(0.2958 0.0327 162.92);
    --primary-900: oklch(0.2513 0.0265 161.96);
    --primary-950: oklch(0.2171 0.0208 162.3);
    --secondary-100: oklch(0.9801 0.0074 80.72);
    --secondary-200: oklch(0.971 0.0114 84.58);
    --secondary-300: oklch(0.9376 0.0239 79.74);
    --secondary-400: oklch(0.7995 0.0764 80.98);
    --secondary-500: oklch(0.7409 0.0703 81.09);
    --secondary-600: oklch(0.6778 0.0642 81.22);
    --secondary-700: oklch(0.647 0.0602 81.54);
    --secondary-800: oklch(0.5506 0.0502 80.89);
    --secondary-900: oklch(0.4498 0.0397 79.81);
    --secondary-950: oklch(0.3803 0.0319 82.02);
    --background-100: oklch(0.9943 0.0028 84.56);
    --background-200: oklch(0.9918 0.0045 78.3);
    --background-300: oklch(0.9828 0.0086 84.57);
    --background-400: oklch(0.9437 0.0278 81.47);
    --background-500: oklch(0.8723 0.0262 84.59);
    --background-600: oklch(0.7985 0.0231 80.67);
    --background-700: oklch(0.7608 0.0221 79.06);
    --background-800: oklch(0.6462 0.0181 79.31);
    --background-900: oklch(0.5261 0.0151 82.38);
    --background-950: oklch(0.4405 0.011 78.21);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.922 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.556 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground;
    }
}
