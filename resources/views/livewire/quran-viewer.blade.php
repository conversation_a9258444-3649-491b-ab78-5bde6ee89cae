<div class="max-w-3xl mx-auto px-4 pt-28 pb-10">
    <style>
        @font-face {
            font-family: 'UthmanicQaloun';
            src: url('{{ asset('fonts/UthmanicQaloun V21.ttf') }}') format('truetype');
        }

        @font-face {
            font-family: 'qaloon-v8';
            src: url('{{ asset('fonts/uthmanic_hafs_v22.ttf') }}') format('truetype');
        }

        body {
            font-family: 'UthmanicQaloun', serif;
            background: linear-gradient(135deg, #ece8e1, #dfd7ca);
            text-align: right;
            margin: 0;
            padding: 0;
        }

        .verse-text {
            font-size: 24px;
            padding: 4px 6px;
            color: #1e1e1e;
            transition: background-color 0.2s, box-shadow 0.2s;
        }

        .verse-text:hover {
            background-color: rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
            border-radius: 6px;
        }

        .tarqim {
            font-family: 'qaloon-v8', serif;
            font-size: 22px;
            color: #5a4e3c;
        }

        /* Glassmorphism */
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
            border-radius: 16px;
        }

        select,
        input[type="text"] {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid #ccc;
        }

        select:focus,
        input[type="text"]:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
            border-color: rgba(59, 130, 246, 0.5);
        }
    </style>

    <!-- Fixed Filter Bar -->
    <div class="fixed top-0 left-0 right-0 z-50 px-4 py-3 glass flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-6 shadow-md">
        <select wire:model.live="selectedChapter" class="px-4 py-2 rounded-md w-full sm:w-auto">
            <option value="">كل الفصول</option>
            @foreach ($chapters as $chapter)
                <option value="{{ $chapter->id }}">{{ $chapter->name }}</option>
            @endforeach
        </select>
        <div class="flex gap-2 items-center">
            <input type="text" wire:model.live="search" class="px-4 py-2 rounded-md w-full sm:w-auto" placeholder="ابحث عن كلمة">
            @if($search)
                <p>
                    {{ $grouped->count() }} آية
                </p>
            @endif
        </div>
    </div>

    <!-- Verses Display -->
    @foreach ($grouped as $key => $verses)
        @php
            $juz = $verses->first()->juz;
            [$chapterId, $page] = explode('{-}', $key);
            $chapterName = $verses->first()->chapter->name ?? 'غير معروف';
        @endphp

        <div class="mb-8 p-6 glass shadow-md" wire:key="group-{{ $key }}">
            {{-- Chapter Name --}}
            @if($verses->first()->number == 1)
                <div class="flex justify-center pb-3">
                    @php
                        $keyChapter = $chapterId.'{-}'.$page;
                    @endphp
                    @livewire('chapter-name', ['name' => $chapterName], key($keyChapter))
                </div>
            @endif

            <!-- Verse text -->
            <div class="text-right text-[22px] text-gray-800 leading-relaxed pb-2" style="text-align: justify;">
                <p class="leading-[48px]">
                    @foreach ($verses as $verse)
                        <span class="verse-text" wire:key="verse-{{ $verse->id }}">
                            @php
                                $words = explode(' ', $verse->text);
                                $lastWord = array_pop($words);
                                $text = implode(' ', $words);
                                preg_match('/^(.*)\x{00A0}(.+)$/u', $lastWord, $matches);
                            @endphp
                            {{ $text }} {{ $matches[1] ?? '' }}
                            <span class="verse-number tarqim">{{ $matches[2] ?? '' }}</span>
                        </span>
                    @endforeach
                </p>
            </div>

            <!-- Page and Chapter Info -->
            <div class="flex justify-between items-center border-t border-gray-300 pt-2 text-sm text-gray-600">
                <div>{{ $chapterName }}</div>
                @php $keyPage = $page.'{-}'.$chapterId; @endphp
                @livewire('page-number', ['page' => $page, 'juz' => $juz], key($keyPage))
            </div>
        </div>
    @endforeach

    <!-- Infinite Scroll Trigger -->
    <div x-intersect.full="$wire.loadMore()" class="p-4 text-center text-gray-600">
        <div wire:loading wire:target="loadMore">
            تحميل المزيد من الآيات...
        </div>
    </div>
</div>
