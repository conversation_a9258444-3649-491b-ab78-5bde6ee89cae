<div class="max-w-7xl mx-auto px-4 py-8 bg-white rounded-lg shadow-md">
    <style>

        @font-face {
            font-family: 'UthmanicQaloun';
            src: url('{{asset('fonts/UthmanTN_v2-0.ttf')}}') format('truetype');
            font-weight: normal;
            font-style: normal;
        }
        body {
            font-family: 'UthmanicQaloun', serif; /* Or another suitable Arabic font */

            text-align: right; /* Default alignment */
            margin: 0;
            padding: 20px;
            background-color: #f8f0e3; /* Off-white, parchment-like background */
        }

        .quran-page {
            direction: rtl;
            max-width: 800px; /* Book-like width */
            margin: 40px auto; /* Center the page with top/bottom margin */
            padding: 30px 40px;
            background-color: #ffffff; /* White content area */
            border: 1px solid #ddd;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1); /* Subtle shadow for depth */
            line-height: 2.2; /* Generous line height for readability */
            font-size: 40px; /* Larger font size for better readability */
        }

        .quran-header, .quran-footer {
            text-align: center;
            font-size: 1.1em;
            color: #555;
            margin-bottom: 20px;
            margin-top: 20px;
        }

        .verse-container {
            display: inline; /* To keep the number next to the text on the same line */
            margin-bottom: 15px; /* Spacing between verses if they wrap */
        }

        .verse-text:hover {
            /* No specific styling needed unless you want specific font weight etc. */
            background-color: #a3a3a3;
            padding: 5px;
            border-radius: 5px;
        }
        .verse-text {
            color: #000000;
            padding: 5px;
        }

        .verse-number {
            font-weight: bold;
            color: #006400; /* Dark green for verse numbers, common in Quran */
            margin-right: 8px; /* Space between number and text */
            vertical-align: super; /* Slightly raise the number */
            font-size: 0.8em; /* Make the number slightly smaller */
        }

        /* Optional: For small screens, adjust layout */
        @media (max-width: 768px) {
            .quran-page {
                margin: 10px;
                padding: 20px;
                font-size: 20px;
            }
        }

        /* Optional: Two-column layout for large screens */
        @media (min-width: 1200px) {
            .quran-content {
                columns: 2;
                column-gap: 40px;
            }
            .verse-container {
                /* Ensure verses don't break across columns in a jarring way */
                break-inside: avoid-column;
            }
        }
    </style>

    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-right">سورة {{ $chapter->number }} - {{ $chapter->name }}</h2>
        <div class="flex gap-2">
            <select wire:model.live="currentChapter"
                    class="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                @foreach(App\Models\Chapter::all() as $chap)
                    <option value="{{ $chap->number }}">{{ $chap->number }} - {{ $chap->name }}</option>
                @endforeach
            </select>
        </div>
    </div>
    <div class="quran-page">
        <div class="quran-header">
        </div>
        <div class="quran-content">
            @foreach($verses as $verse)
                <div class="verse-container">
                    <span class="verse-text">{{ $verse->text }}</span>
                    @php
                        $western = ['0','1','2','3','4','5','6','7','8','9'];
                        $eastern = ['٠','١','٢','٣','٤','٥','٦','٧','٨','٩'];
                        $verseNumberArabic = str_replace($western, $eastern, $verse->number);
                    @endphp

                    <span class="verse-number">{{ $verseNumberArabic }}</span>
                </div>
            @endforeach
        </div>
        <div class="quran-footer">
        </div>
    </div>

    <div class="mt-6">
        {{ $verses->links() }}
    </div>
</div>
