<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class AttendanceSchedule extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'student_id',
        'classroom_id',
        'type',
        'operator_id',
        'operator_type',
        'date',
    ];

    protected $casts = [
        'date' => 'date',
        'type' => 'string',
    ];

    /**
     * Get the student that this attendance record belongs to
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the classroom that this attendance record belongs to
     */
    public function classroom(): BelongsTo
    {
        return $this->belongsTo(Classroom::class);
    }

    /**
     * Get the operator (User or Teacher) who recorded this attendance
     */
    public function operator(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to get present attendance records
     */
    public function scopePresent($query)
    {
        return $query->where('type', 'Present');
    }

    /**
     * Scope to get absent attendance records
     */
    public function scopeAbsent($query)
    {
        return $query->where('type', 'Absent');
    }

    /**
     * Scope to get attendance for a specific date
     */
    public function scopeForDate($query, $date)
    {
        return $query->whereDate('date', $date);
    }

    /**
     * Scope to get attendance for a specific classroom
     */
    public function scopeForClassroom($query, $classroomId)
    {
        return $query->where('classroom_id', $classroomId);
    }

    /**
     * Check if the student is present
     */
    public function isPresent(): bool
    {
        return $this->type === 'Present';
    }

    /**
     * Check if the student is absent
     */
    public function isAbsent(): bool
    {
        return $this->type === 'Absent';
    }
}

