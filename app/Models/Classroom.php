<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Teacher;

/**
 * @property string $teacher_id Reference to the teacher owning the classroom
 */

class Classroom extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'description',
        'teacher_id',
        'capacity',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    public function students(): HasMany
    {
        return $this->hasMany(Student::class);
    }

    public function attendanceRecords(): HasMany
    {
        return $this->hasMany(AttendanceSchedule::class);
    }

    /**
     * Get attendance records for a specific date
     */
    public function attendanceForDate($date): HasMany
    {
        return $this->attendanceRecords()->whereDate('date', $date);
    }

    /**
     * Get present students for a specific date
     */
    public function presentStudentsForDate($date)
    {
        return $this->attendanceRecords()
            ->whereDate('date', $date)
            ->where('type', 'Present')
            ->with('student');
    }

    /**
     * Get absent students for a specific date
     */
    public function absentStudentsForDate($date)
    {
        return $this->attendanceRecords()
            ->whereDate('date', $date)
            ->where('type', 'Absent')
            ->with('student');
    }

    /**
     * Get attendance statistics for a specific date
     */
    public function getAttendanceStatsForDate($date): array
    {
        $totalStudents = $this->students()->count();
        $presentCount = $this->presentStudentsForDate($date)->count();
        $absentCount = $this->absentStudentsForDate($date)->count();

        return [
            'total_students' => $totalStudents,
            'present_count' => $presentCount,
            'absent_count' => $absentCount,
            'attendance_percentage' => $totalStudents > 0 ? ($presentCount / $totalStudents) * 100 : 0,
        ];
    }
}

