<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\AttendanceScheduleResource\Pages;
use App\Models\AttendanceSchedule;
use App\Models\Classroom;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class AttendanceScheduleResource extends Resource
{
    protected static ?string $model = AttendanceSchedule::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?int $navigationSort = 3;

    public static function getPluralModelLabel(): string
    {
        return __('Attendance Records');
    }

    public static function getLabel(): string
    {
        return __('Attendance Record');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('student_id')
                    ->label('Student')
                    ->translateLabel()
                    ->options(Student::query()->pluck('name', 'id'))
                    ->searchable()
                    ->required(),
                Forms\Components\Select::make('classroom_id')
                    ->label('Classroom')
                    ->translateLabel()
                    ->options(Classroom::query()->pluck('name', 'id'))
                    ->required(),
                Forms\Components\Select::make('type')
                    ->label('Attendance Type')
                    ->translateLabel()
                    ->options([
                        'Present' => 'Present',
                        'Absent' => 'Absent',
                    ])
                    ->default('Absent')
                    ->required(),
                Forms\Components\DatePicker::make('date')
                    ->label('Date')
                    ->translateLabel()
                    ->default(now())
                    ->required(),
                Forms\Components\Select::make('operator_type')
                    ->label('Recorded By Type')
                    ->translateLabel()
                    ->options([
                        'App\Models\Teacher' => 'Teacher',
                        'App\Models\User' => 'User',
                    ])
                    ->reactive()
                    ->required(),
                Forms\Components\Select::make('operator_id')
                    ->label('Recorded By')
                    ->translateLabel()
                    ->options(function (callable $get) {
                        $operatorType = $get('operator_type');
                        if ($operatorType === 'App\Models\Teacher') {
                            return Teacher::query()->pluck('name', 'id');
                        } elseif ($operatorType === 'App\Models\User') {
                            return User::query()->pluck('name', 'id');
                        }
                        return [];
                    })
                    ->required(),
            ])->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('student.name')
                    ->label('Student')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('classroom.name')
                    ->label('Classroom')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('type')
                    ->label('Status')
                    ->translateLabel()
                    ->colors([
                        'success' => 'Present',
                        'danger' => 'Absent',
                    ])
                    ->sortable(),
                Tables\Columns\TextColumn::make('date')
                    ->label('Date')
                    ->translateLabel()
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('operator.name')
                    ->label('Recorded By')
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('operator_type')
                    ->label('Operator Type')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'App\Models\Teacher' => 'Teacher',
                        'App\Models\User' => 'User',
                        default => $state,
                    })
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'Present' => 'Present',
                        'Absent' => 'Absent',
                    ]),
                Tables\Filters\SelectFilter::make('classroom_id')
                    ->label('Classroom')
                    ->options(Classroom::query()->pluck('name', 'id')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->defaultSort('date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAttendanceSchedules::route('/'),
            'create' => Pages\CreateAttendanceSchedule::route('/create'),
            'edit' => Pages\EditAttendanceSchedule::route('/{record}/edit'),
        ];
    }
}

