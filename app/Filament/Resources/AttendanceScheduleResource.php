<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\AttendanceScheduleResource\Pages;
use App\Models\AttendanceSchedule;
use App\Models\Classroom;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class AttendanceScheduleResource extends Resource
{
    protected static ?string $model = AttendanceSchedule::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?int $navigationSort = 3;

    public static function getPluralModelLabel(): string
    {
        return __('Attendance Schedules');
    }

    public static function getLabel(): string
    {
        return __('Attendance Schedule');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('classroom_id')
                    ->label('Classroom')
                    ->translateLabel()
                    ->options(Classroom::query()->pluck('name', 'id'))
                    ->required(),
                Forms\Components\DatePicker::make('date')
                    ->label('Date')
                    ->translateLabel()
                    ->required(),
                Forms\Components\TimePicker::make('start_time')
                    ->label('Start Time')
                    ->translateLabel()
                    ->required(),
                Forms\Components\TimePicker::make('end_time')
                    ->label('End Time')
                    ->translateLabel()
                    ->required(),
            ])->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('classroom.name')
                    ->label('Classroom')
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('date')
                    ->label('Date')
                    ->translateLabel()
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_time')
                    ->label('Start Time')
                    ->translateLabel()
                    ->time()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_time')
                    ->label('End Time')
                    ->translateLabel()
                    ->time()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->defaultSort('date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAttendanceSchedules::route('/'),
            'create' => Pages\CreateAttendanceSchedule::route('/create'),
            'edit' => Pages\EditAttendanceSchedule::route('/{record}/edit'),
        ];
    }
}

