<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\ClassroomResource\Pages;
use App\Models\Classroom;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ClassroomResource extends Resource
{
    protected static ?string $model = Classroom::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-library';

    protected static ?int $navigationSort = 4;

    public static function getPluralModelLabel(): string
    {
        return __('Classrooms');
    }

    public static function getLabel(): string
    {
        return __('Classroom');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make(__('Classroom Information'))
                    ->schema([
                        TextInput::make('name')
                            ->label('Name')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255),

                        Textarea::make('description')
                            ->label('Description')
                            ->translateLabel()
                            ->rows(3),

                        Select::make('teacher_id')
                            ->label('Teacher')
                            ->translateLabel()
                            ->relationship('teacher', 'name')
                            ->searchable()
                            ->required(),

                        TextInput::make('capacity')
                            ->label('Capacity')
                            ->translateLabel()
                            ->numeric()
                            ->required()
                            ->minValue(1),

                        Toggle::make('is_active')
                            ->label('Active')
                            ->translateLabel()
                            ->default(true),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('teacher.name')
                    ->label('Teacher')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('capacity')
                    ->label('Capacity')
                    ->translateLabel()
                    ->sortable(),

                IconColumn::make('is_active')
                    ->label('Active')
                    ->translateLabel()
                    ->boolean(),

                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('teacher')
                    ->label('Teacher')
                    ->translateLabel()
                    ->relationship('teacher', 'name')
                    ->searchable(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->translateLabel(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClassrooms::route('/'),
            'create' => Pages\CreateClassroom::route('/create'),
            'edit' => Pages\EditClassroom::route('/{record}/edit'),
        ];
    }
}
