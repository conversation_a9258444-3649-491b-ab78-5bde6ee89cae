<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\GuardianResource\Pages;
use App\Filament\Resources\GuardianResource\Pages\ManegeStudents;
use App\Models\Guardian;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class GuardianResource extends Resource
{
    protected static ?string $model = Guardian::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    protected static ?int $navigationSort = 1;

    public static function getPluralModelLabel(): string
    {
        return __('Guardians');
    }

    public static function getLabel(): string
    {
        return __('Guardian');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Guardian Information'))
                    ->description(__('Enter the guardian\'s personal details'))
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Full Name')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter full name'),

                        Forms\Components\TextInput::make('email')
                            ->label('Email Address')
                            ->translateLabel()
                            ->email()
                            ->maxLength(255)
                            ->placeholder('<EMAIL>'),

                        Forms\Components\TextInput::make('phone')
                            ->label('Phone Number')
                            ->translateLabel()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder('002189123456789'),

                        Forms\Components\Textarea::make('address')
                            ->label('Address')
                            ->translateLabel()
                            ->rows(3)
                            ->placeholder('Enter complete address'),
                    ])->columns(2),

                Forms\Components\Section::make(__('Additional Information'))
                    ->schema([
                        Forms\Components\TextInput::make('national_id')
                            ->label('National ID')
                            ->translateLabel()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder('National ID number'),

                        Forms\Components\TextInput::make('emergency_contact')
                            ->label('Emergency Contact')
                            ->translateLabel()
                            ->maxLength(255)
                            ->placeholder(__('Emergency contact number')),

                        Forms\Components\Select::make('relationship_to_student')
                            ->label('Relationship to Student')
                            ->translateLabel()
                            ->options([
                                'Father' => __('Father'),
                                'Mother' => __('Mother'),
                                'Grandfather' => __('Grandfather'),
                                'Grandmother' => __('Grandmother'),
                                'Uncle' => __('Uncle'),
                                'Aunt' => __('Aunt'),
                                'Guardian' => __('Legal Guardian'),
                                'Other' => __('Other'),
                            ])
                            ->default('Father')
                            ->required(),

                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->translateLabel()
                            ->rows(3)
                            ->placeholder('Any additional notes about the guardian'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Full Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('email')
                    ->label('Email Address')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('phone')
                    ->label('Phone Number')
                    ->translateLabel()
                    ->searchable(),

                Tables\Columns\TextColumn::make('relationship_to_student')
                    ->label('Relationship to Student')
                    ->translateLabel()
                    ->searchable()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'Father' => 'success',
                        'Mother' => 'info',
                        'Guardian' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('students_count')
                    ->label('Students')
                    ->translateLabel()
                    ->counts('students')
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('relationship_to_student')
                    ->label('Relationship to Student')
                    ->translateLabel()
                    ->options([
                        'Father' => __('Father'),
                        'Mother' => __('Mother'),
                        'Grandfather' => __('Grandfather'),
                        'Grandmother' => __('Grandmother'),
                        'Uncle' => __('Uncle'),
                        'Aunt' => __('Aunt'),
                        'Guardian' => __('Legal Guardian'),
                        'Other' => __('Other'),
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\EditGuardian::class,
            ManegeStudents::class,
        ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGuardians::route('/'),
            'create' => Pages\CreateGuardian::route('/create'),
            'edit' => Pages\EditGuardian::route('/{record}/edit'),
            'students' => ManegeStudents::route('/{record}/students'),
        ];
    }
}
