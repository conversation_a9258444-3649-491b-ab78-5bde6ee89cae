<?php

declare(strict_types=1);

namespace App\Filament\Resources\GuardianResource\Pages;

use App\Filament\Resources\GuardianResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;

class ManegeStudents extends ManageRelatedRecords
{
    protected static string $resource = GuardianResource::class;

    protected static string $relationship = 'students';

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    public static function getNavigationLabel(): string
    {
        return __('Students');
    }

    public function getTitle(): string
    {
        return __('Students');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Student Information'))
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Full Name')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255)
                            ->placeholder(__('Enter student full name')),

                        Forms\Components\TextInput::make('age')
                            ->label('Age')
                            ->translateLabel()
                            ->numeric()
                            ->required()
                            ->minValue(3)
                            ->maxValue(25)
                            ->placeholder('Age'),

                        Forms\Components\DatePicker::make('date_of_birth')
                            ->label('Date of Birth')
                            ->translateLabel()
                            ->required()
                            ->maxDate(now())
                            ->displayFormat('Y-m-d'),

                        Forms\Components\Select::make('gender')
                            ->label('Gender')
                            ->translateLabel()
                            ->options([
                                'male' => __('Male'),
                                'female' => __('Female'),
                            ])
                            ->required(),

                        Forms\Components\TextInput::make('grade_level')
                            ->label('Grade Level')
                            ->translateLabel()
                            ->maxLength(255)
                            ->placeholder(__('e.g., Grade 5, High School')),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active Status')
                            ->translateLabel()
                            ->default(true)
                            ->label('Active Student'),
                    ])->columns(2),

                Forms\Components\Section::make(__('Additional Information'))
                    ->schema([
                        Forms\Components\Textarea::make('medical_conditions')
                            ->label('Medical Conditions')
                            ->translateLabel()
                            ->rows(3)
                            ->placeholder(__('Any medical conditions or allergies')),

                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->translateLabel()
                            ->rows(3)
                            ->placeholder(__('Additional notes about the student')),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Full Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('age')
                    ->label('Age')
                    ->translateLabel()
                    ->sortable(),

                Tables\Columns\TextColumn::make('date_of_birth')
                    ->label('Date of Birth')
                    ->translateLabel()
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('gender')
                    ->label('Gender')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'male' => __('Male'),
                        'female' => __('Female'),
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'male' => 'blue',
                        'female' => 'pink',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('grade_level')
                    ->label('Grade Level')
                    ->translateLabel()
                    ->searchable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active Status')
                    ->translateLabel()
                    ->boolean()
                    ->label('Active'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('gender')
                    ->label('Gender')
                    ->options([
                        'male' => __('Male'),
                        'female' => __('Female'),
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->translateLabel(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Add Student')
                    ->translateLabel(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()->slideOver(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
