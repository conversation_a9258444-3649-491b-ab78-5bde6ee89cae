<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\TeacherResource\Pages;
use App\Models\Teacher;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class TeacherResource extends Resource
{
    protected static ?string $model = Teacher::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';

    protected static ?int $navigationSort = 3;

    public static function getPluralModelLabel(): string
    {
        return __('Teachers');
    }

    public static function getLabel(): string
    {
        return __('Teacher');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make(__('Teacher Information'))
                    ->schema([
                        TextInput::make('name')
                            ->label('Full Name')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255),

                        TextInput::make('email')
                            ->label('Email Address')
                            ->translateLabel()
                            ->email()
                            ->maxLength(255),

                        TextInput::make('phone')
                            ->label('Phone Number')
                            ->translateLabel()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),

                        Textarea::make('address')
                            ->label('Address')
                            ->translateLabel()
                            ->rows(3),

                        TextInput::make('national_id')
                            ->label('National ID')
                            ->translateLabel()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),

                        TextInput::make('emergency_contact')
                            ->label('Emergency Contact')
                            ->translateLabel()
                            ->maxLength(255),

                        Textarea::make('notes')
                            ->label('Notes')
                            ->translateLabel()
                            ->rows(3),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Full Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('email')
                    ->label('Email Address')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('phone')
                    ->label('Phone Number')
                    ->translateLabel()
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTeachers::route('/'),
            'create' => Pages\CreateTeacher::route('/create'),
            'edit' => Pages\EditTeacher::route('/{record}/edit'),
        ];
    }
}
