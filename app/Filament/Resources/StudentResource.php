<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\StudentResource\Pages;
use App\Models\Guardian;
use App\Models\Student;
use Filament\Forms;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class StudentResource extends Resource
{
    protected static ?string $model = Student::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?int $navigationSort = 2;

    public static function getPluralModelLabel(): string
    {
        return __('Students');
    }

    public static function getLabel(): string
    {
        return __('Student');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([])->schema([
                    Section::make(__('Student Information'))
                        ->schema([
                            TextInput::make('name')
                                ->label('Full Name')
                                ->translateLabel()
                                ->required()
                                ->maxLength(255)
                                ->placeholder(__('Enter student full name')),

                            TextInput::make('age')
                                ->label('Age')
                                ->translateLabel()
                                ->numeric()
                                ->required()
                                ->minValue(3)
                                ->maxValue(25),

                            Forms\Components\DatePicker::make('date_of_birth')
                                ->label('Date of Birth')
                                ->translateLabel()
                                ->required()
                                ->maxDate(now())
                                ->displayFormat('Y-m-d'),

                            Select::make('gender')
                                ->label('Gender')
                                ->translateLabel()
                                ->options([
                                    'male' => __('Male'),
                                    'female' => __('Female'),
                                ])
                                ->required(),

                            TextInput::make('grade_level')
                                ->label('Grade Level')
                                ->translateLabel()
                                ->maxLength(255)
                                ->placeholder(__('e.g., Grade 5, High School')),

                            Select::make('classroom_id')
                                ->label('Classroom')
                                ->translateLabel()
                                ->options(\App\Models\Classroom::all()->pluck('name', 'id'))
                                ->searchable()
                                ->required(),

                            TextInput::make('registration_number')
                                ->label('Registration Number')
                                ->translateLabel()
                                ->unique(\App\Models\Student::class, 'registration_number', ignoreRecord: true)
                                ->required(),

                            Forms\Components\Toggle::make('is_active')
                                ->default(true)
                                ->label('Active Student')
                                ->translateLabel(),
                        ])->columns(2),

                    Section::make(__('Additional Information'))
                        ->schema([
                            Textarea::make('medical_conditions')
                                ->label('Medical Conditions')
                                ->translateLabel()
                                ->rows(3)
                                ->placeholder(__('Any medical conditions or allergies')),

                            Textarea::make('notes')
                                ->label('Notes')
                                ->translateLabel()
                                ->rows(3)
                                ->placeholder(__('Additional notes about the student')),
                        ]),
                ])->columnSpan(2),
                Group::make([
                    Section::make(__('Guardian Selection'))
                        ->description(__('Select the guardian for this student'))
                        ->schema([
                            Select::make('guardian_id')
                                ->label('Guardian')
                                ->translateLabel()
                                ->options(Guardian::all()->pluck('name', 'id'))
                                ->searchable()
                                ->required()
                                ->createOptionForm([
                                    TextInput::make('name')
                                        ->label('Full Name')
                                        ->translateLabel()
                                        ->required()
                                        ->maxLength(255),
                                    TextInput::make('email')
                                        ->label('Email Address')
                                        ->translateLabel()
                                        ->email()
                                        ->required()
                                        ->unique(Guardian::class),
                                    TextInput::make('phone')
                                        ->label('Phone Number')
                                        ->translateLabel()
                                        ->required(),
                                    Textarea::make('address')
                                        ->label('Address')
                                        ->translateLabel()
                                        ->required(),
                                ])
                                ->createOptionUsing(function (array $data): int {
                                    return Guardian::create($data)->getKey();
                                }),
                            SpatieMediaLibraryFileUpload::make('student_photo')
                                ->collection('student_photo')
                                ->label('Personal Photo')
                                ->translateLabel()
                                ->disk('local')
                                ->visibility('private')
                                ->downloadable(),
                            SpatieMediaLibraryFileUpload::make('student_documents')
                                ->collection('student_documents')
                                ->label('Documents')
                                ->translateLabel()
                                ->disk('local')
                                ->multiple()
                                ->visibility('private')
                                ->downloadable(),
                        ]),
                ])->columnSpan(1),
            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('guardian.name')
                    ->label('Guardian')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('classroom.name')
                    ->label('Classroom')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('age')
                    ->label('Age')
                    ->translateLabel()
                    ->sortable(),

                Tables\Columns\TextColumn::make('date_of_birth')
                    ->label('Date of Birth')
                    ->translateLabel()
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('gender')
                    ->label('Gender')
                    ->translateLabel()
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'male' => 'blue',
                        'female' => 'pink',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('grade_level')
                    ->label('Grade Level')
                    ->translateLabel()
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('registration_number')
                    ->label('Registration Number')
                    ->translateLabel()
                    ->sortable()
                    ->searchable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->translateLabel()
                    ->boolean()
                    ->label('Active'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('guardian')
                    ->label('Guardian')
                    ->translateLabel()
                    ->relationship('guardian', 'name')
                    ->searchable(),

                Tables\Filters\SelectFilter::make('classroom')
                    ->label('Classroom')
                    ->translateLabel()
                    ->relationship('classroom', 'name')
                    ->searchable(),

                Tables\Filters\SelectFilter::make('gender')
                    ->label('Gender')
                    ->translateLabel()
                    ->options([
                        'male' => __('Male'),
                        'female' => __('Female'),
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->translateLabel(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStudents::route('/'),
            'create' => Pages\CreateStudent::route('/create'),
            'edit' => Pages\EditStudent::route('/{record}/edit'),
        ];
    }
}
