<?php

declare(strict_types=1);

use App\Services\AI\TemplateGenerator;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Config;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();

    // Set up test configuration
    Config::set('message_check.token', 'test-token');
    Config::set('message_check.url', 'https://test-api.example.com/');
    Config::set('message_check.model', 'test-model');
});

// Helper function to create a mock TemplateGenerator with mocked HTTP client
function createGeneratorWithMockedClient(array $responses): TemplateGenerator
{
    $mock = new MockHandler($responses);
    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    return new class($client) extends TemplateGenerator
    {
        private Client $mockClient;

        public function __construct(Client $mockClient)
        {
            parent::__construct();
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };
}

it('can generate a template successfully', function () {
    // Mock successful API response
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'مرحباً {{name}}! نرحب بك في {{company_name}}.',
                                'parameters' => [
                                    ['name' => 'name', 'description' => 'User name'],
                                    ['name' => 'company_name', 'description' => 'Company name'],
                                ],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('مرحباً {{name}}! نرحب بك في {{company_name}}.');
    expect($result['parameters'])->toHaveCount(2);
    expect($result['parameters'][0]['name'])->toBe('name');
    expect($result['parameters'][1]['name'])->toBe('company_name');
});

it('handles API failure gracefully', function () {
    $generator = createGeneratorWithMockedClient([
        new Response(500, [], 'Internal Server Error'),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('API request failed');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('handles request exception gracefully', function () {
    $generator = createGeneratorWithMockedClient([
        new RequestException('Connection timeout', new Request('POST', 'test')),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toContain('Request failed');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('handles invalid JSON response gracefully', function () {
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => 'Invalid JSON response',
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Failed to parse response');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('extracts parameters from content when not provided', function () {
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Hello {{name}}, your code is {{code}}.',
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $result = $generator->generate('verification', 'Send verification code', 'en');

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}}, your code is {{code}}.');
    expect($result['parameters'])->toHaveCount(2);
    expect($result['parameters'][0]['name'])->toBe('name');
    expect($result['parameters'][1]['name'])->toBe('code');
});

it('builds correct prompt for different template types and languages', function () {
    $generator = new TemplateGenerator();

    // Use reflection to test the private buildPrompt method
    $reflection = new ReflectionClass($generator);
    $method = $reflection->getMethod('buildPrompt');
    $method->setAccessible(true);

    $prompt = $method->invoke($generator, 'welcome', 'Welcome message for new users', 'ar');

    expect($prompt)->toContain('Generate the template in Arabic language');
    expect($prompt)->toContain('Template Type: welcome');
    expect($prompt)->toContain('Description: Welcome message for new users');
    expect($prompt)->toContain('{{parameters}}');
});

it('builds correct prompt for English language', function () {
    $generator = new TemplateGenerator();

    // Use reflection to test the private buildPrompt method
    $reflection = new ReflectionClass($generator);
    $method = $reflection->getMethod('buildPrompt');
    $method->setAccessible(true);

    $prompt = $method->invoke($generator, 'notification', 'Send notification to users', 'en');

    expect($prompt)->toContain('Generate the template in English language');
    expect($prompt)->toContain('Template Type: notification');
    expect($prompt)->toContain('Description: Send notification to users');
});

it('handles missing text in API response', function () {
    // Mock API response with missing text
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => null,
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Missing text in response');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('handles JSON exception during encoding', function () {
    // Create a mock that will cause json_decode to fail with invalid JSON
    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode([
            'candidates' => [
                [
                    'content' => [
                        'parts' => [
                            [
                                'text' => '{"invalid": json}', // Invalid JSON syntax
                            ],
                        ],
                    ],
                ],
            ],
        ])),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Failed to parse response');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('handles response with invalid content format', function () {
    // Mock API response with invalid content structure
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'invalid' => 'structure',
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Invalid response format');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('handles response with markdown formatting', function () {
    // Mock API response with markdown-formatted JSON
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => "```json\n".json_encode([
                                'content' => 'Hello {{name}}!',
                                'parameters' => [
                                    ['name' => 'name', 'description' => 'User name'],
                                ],
                            ])."\n```",
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}}!');
    expect($result['parameters'])->toHaveCount(1);
    expect($result['parameters'][0]['name'])->toBe('name');
});

it('handles response with invalid parameters structure', function () {
    // Mock API response with invalid parameters
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Hello {{name}}!',
                                'parameters' => 'invalid_parameters', // Should be array
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}}!');
    expect($result['parameters'])->toHaveCount(1);
    expect($result['parameters'][0]['name'])->toBe('name');
    expect($result['parameters'][0]['description'])->toBe('Dynamic parameter: name');
});

it('handles response with malformed parameter objects', function () {
    // Mock API response with malformed parameter objects
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Hello {{name}} and {{age}}!',
                                'parameters' => [
                                    ['name' => 'name'], // Missing description
                                    'invalid_param', // Not an object
                                    ['description' => 'Age'], // Missing name
                                    ['name' => 'valid', 'description' => 'Valid param'], // Valid
                                ],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}} and {{age}}!');
    expect($result['parameters'])->toHaveCount(1); // Only valid parameter should be included
    expect($result['parameters'][0]['name'])->toBe('valid');
    expect($result['parameters'][0]['description'])->toBe('Valid param');
});

it('tests different template types in getTemplateExamples', function () {
    $generator = new TemplateGenerator();

    // Use reflection to test the private getTemplateExamples method
    $reflection = new ReflectionClass($generator);
    $method = $reflection->getMethod('getTemplateExamples');
    $method->setAccessible(true);

    $examples = $method->invoke($generator, 'reminder', 'ar');

    expect($examples)->toContain('تذكير'); // Arabic word for reminder
    expect($examples)->toContain('{{date}}');
    expect($examples)->toContain('{{event}}');
    expect($examples)->toContain('{{time}}');
});

it('handles empty response from API', function () {
    $generator = createGeneratorWithMockedClient([
        new Response(200, [], ''),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toContain('JSON parsing failed');
});

it('handles response with empty candidates array', function () {
    $mockResponse = ['candidates' => []];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Missing text in response');
});

it('handles response with missing candidates key', function () {
    $mockResponse = ['invalid' => 'structure'];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Missing text in response');
});

it('tests getTemplateExamples with English language', function () {
    $generator = new TemplateGenerator();

    // Use reflection to test the private getTemplateExamples method
    $reflection = new ReflectionClass($generator);
    $method = $reflection->getMethod('getTemplateExamples');
    $method->setAccessible(true);

    $examples = $method->invoke($generator, 'verification', 'en');

    expect($examples)->toContain('Your verification code is');
    expect($examples)->toContain('{{code}}');
    expect($examples)->toContain('Do not share');
});

it('tests getTemplateExamples with unknown template type falls back to notification', function () {
    $generator = new TemplateGenerator();

    // Use reflection to test the private getTemplateExamples method
    $reflection = new ReflectionClass($generator);
    $method = $reflection->getMethod('getTemplateExamples');
    $method->setAccessible(true);

    $examples = $method->invoke($generator, 'unknown_type', 'ar');

    expect($examples)->toContain('عزيزي'); // Should fall back to notification template
    expect($examples)->toContain('{{name}}');
    expect($examples)->toContain('{{action}}');
});

it('handles response with empty parameters array', function () {
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Hello {{name}}!',
                                'parameters' => [],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}}!');
    expect($result['parameters'])->toHaveCount(1);
    expect($result['parameters'][0]['name'])->toBe('name');
    expect($result['parameters'][0]['description'])->toBe('Dynamic parameter: name');
});
