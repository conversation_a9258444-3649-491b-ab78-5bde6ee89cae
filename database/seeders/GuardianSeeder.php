<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Guardian;
use Illuminate\Database\Seeder;

class GuardianSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $guardians = [
            [
                'name' => '<PERSON>',
                'email' => 'ahmed.<PERSON><EMAIL>',
                'phone' => '+966501234567',
                'address' => '123 Al-Madinah Street, Riyadh, Saudi Arabia',
                'national_id' => '1234567890',
                'emergency_contact' => '+966501234568',
                'relationship_to_student' => 'Father',
                'notes' => 'Prefers communication in Arabic',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+966502345678',
                'address' => '456 Al-Noor Avenue, Jeddah, Saudi Arabia',
                'national_id' => '2345678901',
                'emergency_contact' => '+966502345679',
                'relationship_to_student' => 'Father',
                'notes' => 'Works night shifts, prefers evening calls',
            ],
            [
                'name' => '<PERSON><PERSON> Al-Zah<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+966503456789',
                'address' => '789 Al-Salam District, Mecca, Saudi Arabia',
                'national_id' => '3456789012',
                'emergency_contact' => '+966503456790',
                'relationship_to_student' => 'Mother',
                'notes' => 'Single mother, primary guardian',
            ],
            [
                'name' => 'Abdullah Al-Rashid',
                'email' => '<EMAIL>',
                'phone' => '+966504567890',
                'address' => '321 Al-Hijra Street, Medina, Saudi Arabia',
                'national_id' => '4567890123',
                'emergency_contact' => '+966504567891',
                'relationship_to_student' => 'Father',
                'notes' => 'Has multiple children enrolled',
            ],
            [
                'name' => 'Khadija Bint Khuwaylid',
                'email' => '<EMAIL>',
                'phone' => '+966505678901',
                'address' => '654 Al-Barakah Compound, Dammam, Saudi Arabia',
                'national_id' => '5678901234',
                'emergency_contact' => '+966505678902',
                'relationship_to_student' => 'Mother',
                'notes' => 'Teacher at local school',
            ],
        ];

        foreach ($guardians as $guardian) {
            Guardian::create($guardian);
        }
    }
}
