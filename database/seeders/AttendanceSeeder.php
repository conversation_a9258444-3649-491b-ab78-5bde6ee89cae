<?php

namespace Database\Seeders;

use App\Models\AttendanceSchedule;
use App\Models\Classroom;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class AttendanceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing data
        $classrooms = Classroom::with('students')->get();
        $teachers = Teacher::all();
        $users = User::all();

        if ($classrooms->isEmpty() || ($teachers->isEmpty() && $users->isEmpty())) {
            $this->command->info('No classrooms, students, teachers, or users found. Please seed those first.');
            return;
        }

        // Create attendance records for the last 7 days
        $dates = collect();
        for ($i = 6; $i >= 0; $i--) {
            $dates->push(Carbon::now()->subDays($i)->format('Y-m-d'));
        }

        foreach ($classrooms as $classroom) {
            if ($classroom->students->isEmpty()) {
                continue;
            }

            foreach ($dates as $date) {
                foreach ($classroom->students as $student) {
                    // Randomly assign Present/Absent (80% chance of being present)
                    $type = rand(1, 100) <= 80 ? 'Present' : 'Absent';

                    // Randomly choose operator (teacher or user)
                    $operators = collect();
                    if ($teachers->isNotEmpty()) {
                        $operators = $operators->merge($teachers->map(fn($t) => ['id' => $t->id, 'type' => 'App\Models\Teacher']));
                    }
                    if ($users->isNotEmpty()) {
                        $operators = $operators->merge($users->map(fn($u) => ['id' => $u->id, 'type' => 'App\Models\User']));
                    }

                    $operator = $operators->random();

                    AttendanceSchedule::create([
                        'student_id' => $student->id,
                        'classroom_id' => $classroom->id,
                        'type' => $type,
                        'operator_id' => $operator['id'],
                        'operator_type' => $operator['type'],
                        'date' => $date,
                    ]);
                }
            }
        }

        $this->command->info('Attendance records created successfully for the last 7 days!');
    }
}
