<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Classroom;
use App\Models\Guardian;
use App\Models\Student;
use Illuminate\Database\Seeder;

class StudentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $guardians = Guardian::all();
        $classrooms = Classroom::factory()->count(5)->create();

        if ($guardians->isEmpty()) {
            $this->command->warn('No guardians found. Please run GuardianSeeder first.');

            return;
        }

        $students = [
            [
                'guardian_id' => $guardians->first()->id,
                'classroom_id' => $classrooms->first()->id,
                'registration_number' => 'REG007',
                'name' => '<PERSON>',
                'age' => 8,
                'date_of_birth' => '2016-03-15',
                'grade_level' => 'Grade 3',
                'gender' => 'male',
                'medical_conditions' => null,
                'notes' => 'Excellent memorization skills',
                'is_active' => true,
            ],
            [
                'guardian_id' => $guardians->first()->id,
                'classroom_id' => $classrooms->first()->id,
                'registration_number' => 'REG006',
                'name' => '<PERSON><PERSON>',
                'age' => 6,
                'date_of_birth' => '2018-07-22',
                'grade_level' => 'Grade 1',
                'gender' => 'female',
                'medical_conditions' => null,
                'notes' => 'Loves recitation',
                'is_active' => true,
            ],
            [
                'guardian_id' => $guardians->skip(1)->first()->id,
                'classroom_id' => $classrooms->first()->id,
                'registration_number' => 'REG005',
                'name' => 'Yusuf Omar Ibn Khattab',
                'age' => 10,
                'date_of_birth' => '2014-11-08',
                'grade_level' => 'Grade 5',
                'gender' => 'male',
                'medical_conditions' => 'Mild asthma',
                'notes' => 'Needs inhaler during physical activities',
                'is_active' => true,
            ],
            [
                'guardian_id' => $guardians->skip(2)->first()->id,
                'classroom_id' => $classrooms->first()->id,
                'registration_number' => 'REG004',
                'name' => 'Maryam Fatima Al-Zahra',
                'age' => 7,
                'date_of_birth' => '2017-05-12',
                'grade_level' => 'Grade 2',
                'gender' => 'female',
                'medical_conditions' => null,
                'notes' => 'Very dedicated student',
                'is_active' => true,
            ],
            [
                'guardian_id' => $guardians->skip(3)->first()->id,
                'classroom_id' => $classrooms->first()->id,
                'registration_number' => 'REG003',
                'name' => 'Hassan Abdullah Al-Rashid',
                'age' => 9,
                'date_of_birth' => '2015-09-30',
                'grade_level' => 'Grade 4',
                'gender' => 'male',
                'medical_conditions' => null,
                'notes' => 'Good at Tajweed',
                'is_active' => true,
            ],
            [
                'guardian_id' => $guardians->skip(3)->first()->id,
                'classroom_id' => $classrooms->first()->id,
                'registration_number' => 'REG002',
                'name' => 'Zainab Abdullah Al-Rashid',
                'age' => 11,
                'date_of_birth' => '2013-01-18',
                'grade_level' => 'Grade 6',
                'gender' => 'female',
                'medical_conditions' => null,
                'notes' => 'Advanced student, helps younger children',
                'is_active' => true,
            ],
            [
                'guardian_id' => $guardians->skip(4)->first()->id,
                'classroom_id' => $classrooms->first()->id,
                'registration_number' => 'REG001',
                'name' => 'Ali Khadija Bint Khuwaylid',
                'age' => 12,
                'date_of_birth' => '2012-12-05',
                'grade_level' => 'Grade 7',
                'gender' => 'male',
                'medical_conditions' => 'Food allergies (nuts)',
                'notes' => 'Allergic to nuts - keep EpiPen available',
                'is_active' => true,
            ],
        ];

        foreach ($students as $student) {
            Student::create($student);
        }
    }
}
