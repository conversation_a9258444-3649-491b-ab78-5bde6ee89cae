<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, clear any existing data since we're changing the structure completely
        \DB::table('attendance_schedules')->truncate();

        Schema::table('attendance_schedules', function (Blueprint $table) {
            // Remove old scheduling fields first
            $table->dropColumn(['start_time', 'end_time']);

            // Add new fields for daily attendance records
            $table->foreignUuid('student_id')->after('classroom_id')->constrained('students')->cascadeOnDelete();
            $table->enum('type', ['Present', 'Absent'])->after('date')->default('Absent');
            $table->uuid('operator_id')->after('type');
            $table->string('operator_type')->after('operator_id');

            // Add unique constraint to prevent duplicate attendance records
            $table->unique(['student_id', 'classroom_id', 'date'], 'unique_student_classroom_date');

            // Add index for polymorphic relationship
            $table->index(['operator_id', 'operator_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('attendance_schedules', function (Blueprint $table) {
            // Drop the unique constraint
            $table->dropUnique('unique_student_classroom_date');

            // Drop the polymorphic index
            $table->dropIndex(['operator_id', 'operator_type']);

            // Remove new fields
            $table->dropForeign(['student_id']);
            $table->dropColumn(['student_id', 'type', 'operator_id', 'operator_type']);

            // Restore old scheduling fields
            $table->time('start_time')->after('date');
            $table->time('end_time')->after('start_time');
        });
    }
};
