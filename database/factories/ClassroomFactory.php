<?php

namespace Database\Factories;

use App\Models\Classroom;
use App\Models\Teacher;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClassroomFactory extends Factory
{
    protected $model = Classroom::class;

    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'description' => fake()->text(),
            'teacher_id' => Teacher::factory(),
            'capacity' => fake()->numberBetween(10, 30),
            'is_active' => fake()->boolean(),
        ];
    }
}
